import Image from 'next/image'
import Link from 'next/link'
import { Shield, Target, Users, Award, Heart, Star } from 'lucide-react'

const nccValues = [
  {
    icon: Shield,
    title: "Character",
    description: "Building moral strength and ethical leadership through disciplined training and value-based education."
  },
  {
    icon: Users,
    title: "Comradeship",
    description: "Fostering unity, teamwork, and mutual respect among cadets from diverse backgrounds."
  },
  {
    icon: Target,
    title: "Discipline",
    description: "Instilling self-control, punctuality, and adherence to rules and regulations."
  },
  {
    icon: Award,
    title: "Leadership",
    description: "Developing command abilities and decision-making skills for future responsibilities."
  },
  {
    icon: Heart,
    title: "Secular Outlook",
    description: "Promoting religious harmony and national integration among all communities."
  },
  {
    icon: Star,
    title: "Spirit of Adventure",
    description: "Encouraging courage, risk-taking, and exploration of new challenges and experiences."
  }
]

const milestones = [
  {
    year: "2009",
    title: "NCC Unit Establishment",
    description: "NCC unit was established at SRMIST Tiruchirappalli with the first batch of 50 cadets."
  },
  {
    year: "2012",
    title: "First State Level Recognition",
    description: "Our cadets participated in state-level competitions and won multiple awards."
  },
  {
    year: "2015",
    title: "Expansion to 200 Cadets",
    description: "Unit strength increased to 200 cadets with enhanced training facilities."
  },
  {
    year: "2018",
    title: "Best Unit Award",
    description: "Received the Best NCC Unit award at the state level for outstanding performance."
  },
  {
    year: "2020",
    title: "Digital Training Initiative",
    description: "Introduced digital training modules and virtual camps during the pandemic."
  },
  {
    year: "2023",
    title: "500+ Cadets Strong",
    description: "Reached a milestone of 500+ active cadets with modern training infrastructure."
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About NCC SRMIST
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Building character, leadership, and patriotism among the youth through 
              comprehensive training and value-based education since 2009.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* NCC Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                National Cadet Corps
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  The National Cadet Corps (NCC) is a voluntary organization which recruits 
                  cadets from high schools, colleges and universities all over India. The NCC 
                  is one of the largest uniformed youth organizations in the world.
                </p>
                <p>
                  At SRMIST Tiruchirappalli, our NCC unit operates under the 2 Tamil Nadu (BN) 
                  Trichy Battalion, providing comprehensive training to develop character, 
                  comradeship, discipline, leadership, secular outlook, spirit of adventure, 
                  and ideals of selfless service among the youth.
                </p>
                <p>
                  Our motto "Unity and Discipline" reflects our commitment to building a 
                  strong, unified, and disciplined force of young leaders who will serve 
                  the nation with dedication and integrity.
                </p>
              </div>
              <div className="mt-8">
                <Link
                  href="/enrollment"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200"
                >
                  Join Our Unit
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="/about/ncc-overview.jpg"
                  alt="NCC Overview"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-xl shadow-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">15+</div>
                  <div className="text-sm text-gray-600">Years of Excellence</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* NCC Values */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              The NCC aims to develop these fundamental qualities in every cadet, 
              shaping them into responsible citizens and future leaders.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {nccValues.map((value, index) => {
              const Icon = value.icon
              return (
                <div
                  key={index}
                  className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 ml-3">
                      {value.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Battalion Information */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="/about/battalion.jpg"
                  alt="2 TN BN Trichy"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                2 Tamil Nadu (BN) Trichy Battalion
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  Our NCC unit is proudly affiliated with the 2 Tamil Nadu Battalion, 
                  Tiruchirappalli, which oversees NCC activities across the region. 
                  The battalion has a rich history of producing outstanding cadets 
                  who have served the nation with distinction.
                </p>
                <p>
                  Under the guidance of experienced officers and instructors, our 
                  cadets receive comprehensive training in various aspects including 
                  drill, weapon training, map reading, first aid, disaster management, 
                  and leadership development.
                </p>
                <p>
                  The battalion regularly organizes camps, competitions, and social 
                  service activities that provide practical experience and help 
                  cadets develop essential life skills.
                </p>
              </div>
              
              <div className="mt-8 grid grid-cols-2 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">50+</div>
                  <div className="text-sm text-gray-600">Affiliated Institutions</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">2000+</div>
                  <div className="text-sm text-gray-600">Active Cadets</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Journey
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              A timeline of our growth, achievements, and milestones over the years.
            </p>
          </div>
          
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-blue-200"></div>
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div
                  key={index}
                  className={`flex items-center ${
                    index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                  }`}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
                    <div className="bg-white rounded-xl p-6 shadow-md">
                      <div className="text-2xl font-bold text-blue-600 mb-2">
                        {milestone.year}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {milestone.title}
                      </h3>
                      <p className="text-gray-600">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                  
                  {/* Timeline dot */}
                  <div className="relative z-10 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Begin Your NCC Journey?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Join us in building character, developing leadership skills, and serving the nation. 
            Become part of a legacy that has shaped thousands of young leaders.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/enrollment"
              className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Apply for Enrollment
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold rounded-lg transition-colors duration-200"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
