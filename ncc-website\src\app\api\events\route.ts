import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/lib/database'
import { Event } from '@/lib/models'

// GET /api/events - Fetch all events with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const search = searchParams.get('search')

    // Build filter object
    const filter: any = {}
    
    if (category && category !== 'all') {
      filter.category = category
    }
    
    if (status && status !== 'all') {
      filter.status = status
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ]
    }

    const events = await Database.getEvents(filter, limit ? parseInt(limit) : undefined)
    
    return NextResponse.json({
      success: true,
      data: events,
      count: events.length
    })
  } catch (error) {
    console.error('Error fetching events:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch events' },
      { status: 500 }
    )
  }
}

// POST /api/events - Create a new event (Admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'date', 'location', 'category']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Create event object
    const eventData: Omit<Event, '_id'> = {
      title: body.title,
      description: body.description,
      date: new Date(body.date),
      location: body.location,
      images: body.images || [],
      category: body.category,
      status: body.status || 'upcoming',
      registrationRequired: body.registrationRequired || false,
      maxParticipants: body.maxParticipants,
      currentParticipants: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = await Database.createEvent(eventData)
    
    if (result.acknowledged) {
      return NextResponse.json({
        success: true,
        message: 'Event created successfully',
        data: { id: result.insertedId }
      }, { status: 201 })
    } else {
      throw new Error('Failed to create event')
    }
  } catch (error) {
    console.error('Error creating event:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create event' },
      { status: 500 }
    )
  }
}

// PUT /api/events - Update an event (Admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Event ID is required' },
        { status: 400 }
      )
    }

    // Remove fields that shouldn't be updated directly
    delete updateData._id
    delete updateData.createdAt
    updateData.updatedAt = new Date()

    const result = await Database.updateEvent(id, updateData)
    
    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Event not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Event updated successfully'
    })
  } catch (error) {
    console.error('Error updating event:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update event' },
      { status: 500 }
    )
  }
}

// DELETE /api/events - Delete an event (Admin only)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Event ID is required' },
        { status: 400 }
      )
    }

    const result = await Database.deleteEvent(id)
    
    if (result.deletedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Event not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Event deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting event:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete event' },
      { status: 500 }
    )
  }
}
