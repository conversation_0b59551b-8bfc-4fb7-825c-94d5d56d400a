'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Trophy, Medal, Award, Star, Filter, Search, Calendar, User } from 'lucide-react'
import { formatDate } from '@/lib/utils'

// Mock data - in real app, this would come from API
const achievements = [
  {
    id: 1,
    title: "Best NCC Unit Award 2023",
    description: "SRMIST NCC unit was awarded the Best NCC Unit at the state level for outstanding performance in training, discipline, and community service.",
    cadetName: "Unit Achievement",
    cadetRank: "Unit Level",
    category: "medal",
    year: 2023,
    date: new Date('2023-11-15'),
    image: "/achievements/best-unit-2023.jpg",
    awardedBy: "Tamil Nadu NCC Directorate"
  },
  {
    id: 2,
    title: "State Level Drill Competition - 1st Place",
    description: "Cadet <PERSON><PERSON> secured first position in the state-level drill competition, demonstrating exceptional precision and discipline.",
    cadetName: "<PERSON><PERSON>",
    cadetRank: "Sergeant",
    category: "competition",
    year: 2023,
    date: new Date('2023-10-20'),
    image: "/achievements/drill-competition.jpg",
    awardedBy: "Tamil Nadu NCC"
  },
  {
    id: 3,
    title: "National Integration Camp Certificate",
    description: "Cadet <PERSON><PERSON> successfully completed the National Integration Camp and received a certificate of excellence for leadership and cultural integration.",
    cadetName: "<PERSON><PERSON> <PERSON>",
    cadetRank: "Corporal",
    category: "certificate",
    year: 2023,
    date: new Date('2023-09-10'),
    image: "/achievements/nic-certificate.jpg",
    awardedBy: "National NCC Directorate"
  },
  {
    id: 4,
    title: "Blood Donation Recognition",
    description: "Recognition for organizing the largest blood donation camp in the district, collecting over 500 units of blood for local hospitals.",
    cadetName: "Social Service Wing",
    cadetRank: "Unit Level",
    category: "other",
    year: 2023,
    date: new Date('2023-08-15'),
    image: "/achievements/blood-donation.jpg",
    awardedBy: "District Collector"
  },
  {
    id: 5,
    title: "Adventure Training Excellence",
    description: "Cadet Arun Patel completed advanced adventure training including rock climbing, trekking, and survival skills with distinction.",
    cadetName: "Arun Patel",
    cadetRank: "Lance Corporal",
    category: "camp",
    year: 2023,
    date: new Date('2023-07-25'),
    image: "/achievements/adventure-training.jpg",
    awardedBy: "Adventure Training Institute"
  },
  {
    id: 6,
    title: "Inter-College Shooting Championship",
    description: "Cadet Meera Singh won the inter-college shooting championship, scoring 98/100 in the precision shooting category.",
    cadetName: "Meera Singh",
    cadetRank: "Cadet",
    category: "competition",
    year: 2023,
    date: new Date('2023-06-30'),
    image: "/achievements/shooting-championship.jpg",
    awardedBy: "Inter-College Sports Committee"
  },
  {
    id: 7,
    title: "Community Service Excellence Award",
    description: "Recognition for exceptional community service during flood relief operations, helping over 200 families in affected areas.",
    cadetName: "Disaster Relief Team",
    cadetRank: "Unit Level",
    category: "medal",
    year: 2022,
    date: new Date('2022-12-10'),
    image: "/achievements/community-service.jpg",
    awardedBy: "State Government"
  },
  {
    id: 8,
    title: "Leadership Training Certificate",
    description: "Cadet Vikram Reddy completed advanced leadership training and was certified as a potential officer candidate.",
    cadetName: "Vikram Reddy",
    cadetRank: "Company Sergeant Major",
    category: "certificate",
    year: 2022,
    date: new Date('2022-11-20'),
    image: "/achievements/leadership-certificate.jpg",
    awardedBy: "NCC Training Command"
  }
]

const categories = [
  { value: 'all', label: 'All Categories', icon: Trophy },
  { value: 'medal', label: 'Medals & Awards', icon: Medal },
  { value: 'certificate', label: 'Certificates', icon: Award },
  { value: 'competition', label: 'Competitions', icon: Star },
  { value: 'camp', label: 'Camps & Training', icon: Trophy },
  { value: 'other', label: 'Other Achievements', icon: Award }
]

const years = [2023, 2022, 2021, 2020, 2019]

export default function AchievementsPage() {
  const [filteredAchievements, setFilteredAchievements] = useState(achievements)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedYear, setSelectedYear] = useState('all')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    let filtered = achievements

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(achievement =>
        achievement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        achievement.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        achievement.cadetName.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(achievement => achievement.category === selectedCategory)
    }

    // Filter by year
    if (selectedYear !== 'all') {
      filtered = filtered.filter(achievement => achievement.year === parseInt(selectedYear))
    }

    setFilteredAchievements(filtered)
  }, [searchTerm, selectedCategory, selectedYear])

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(cat => cat.value === category)
    return categoryData ? categoryData.icon : Trophy
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'medal':
        return 'bg-yellow-100 text-yellow-800'
      case 'certificate':
        return 'bg-blue-100 text-blue-800'
      case 'competition':
        return 'bg-purple-100 text-purple-800'
      case 'camp':
        return 'bg-green-100 text-green-800'
      case 'other':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const stats = [
    { label: 'Total Achievements', value: achievements.length },
    { label: 'State Level Awards', value: achievements.filter(a => a.awardedBy.includes('State')).length },
    { label: 'National Recognition', value: achievements.filter(a => a.awardedBy.includes('National')).length },
    { label: 'This Year', value: achievements.filter(a => a.year === 2023).length }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-yellow-600 via-orange-600 to-red-600 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Trophy className="h-16 w-16 mx-auto mb-6 text-yellow-200" />
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our Achievements
            </h1>
            <p className="text-xl md:text-2xl text-orange-100 max-w-4xl mx-auto leading-relaxed">
              Celebrating excellence, dedication, and outstanding performance of our NCC cadets
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-yellow-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search achievements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              />
            </div>

            {/* Filter Toggle (Mobile) */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden flex items-center px-4 py-2 bg-white hover:bg-gray-100 rounded-lg transition-colors duration-200 border border-gray-300"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>

            {/* Filters (Desktop) */}
            <div className="hidden lg:flex gap-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>

              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white"
              >
                <option value="all">All Years</option>
                {years.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Mobile Filters */}
          {showFilters && (
            <div className="lg:hidden mt-4 p-4 bg-white rounded-lg border border-gray-300">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                >
                  <option value="all">All Years</option>
                  {years.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Achievements Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredAchievements.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Trophy className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No achievements found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-gray-900">
                  Our Achievements
                </h2>
                <span className="text-gray-600">
                  {filteredAchievements.length} achievement{filteredAchievements.length !== 1 ? 's' : ''} found
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredAchievements.map((achievement) => {
                  const CategoryIcon = getCategoryIcon(achievement.category)
                  return (
                    <div key={achievement.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200 overflow-hidden border border-gray-100">
                      <div className="relative h-48">
                        <Image
                          src={achievement.image}
                          alt={achievement.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute top-4 left-4 flex gap-2">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(achievement.category)}`}>
                            {achievement.category.charAt(0).toUpperCase() + achievement.category.slice(1)}
                          </span>
                          <span className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-xs font-medium">
                            {achievement.year}
                          </span>
                        </div>
                        <div className="absolute top-4 right-4">
                          <div className="p-2 bg-yellow-500 rounded-full">
                            <CategoryIcon className="h-4 w-4 text-white" />
                          </div>
                        </div>
                      </div>

                      <div className="p-6">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                          {achievement.title}
                        </h3>
                        <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                          {achievement.description}
                        </p>

                        <div className="space-y-2 text-sm text-gray-600 mb-4">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2" />
                            <span>{achievement.cadetName}</span>
                            {achievement.cadetRank !== 'Unit Level' && (
                              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                {achievement.cadetRank}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2" />
                            <span>{formatDate(achievement.date)}</span>
                          </div>
                          <div className="flex items-center">
                            <Award className="h-4 w-4 mr-2" />
                            <span>{achievement.awardedBy}</span>
                          </div>
                        </div>

                        <div className="pt-4 border-t border-gray-100">
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Achievement #{achievement.id}</span>
                            <div className="flex items-center text-yellow-600">
                              <Trophy className="h-4 w-4 mr-1" />
                              <span className="text-sm font-medium">Recognized</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-yellow-600 to-orange-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Trophy className="h-12 w-12 mx-auto mb-6 text-yellow-200" />
          <h2 className="text-3xl font-bold mb-4">
            Be Part of Our Success Story
          </h2>
          <p className="text-xl text-orange-100 mb-8 max-w-3xl mx-auto">
            Join NCC and create your own legacy of excellence, leadership, and service to the nation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/enrollment"
              className="inline-flex items-center px-8 py-3 bg-white text-orange-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Join NCC Today
            </a>
            <a
              href="/activities"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-orange-600 font-semibold rounded-lg transition-colors duration-200"
            >
              Explore Activities
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
