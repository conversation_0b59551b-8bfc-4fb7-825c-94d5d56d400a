'use client'

import { useState, useEffect, useRef } from 'react'
import { Users, Calendar, Trophy, Award, Target, Shield } from 'lucide-react'

const stats = [
  {
    id: 1,
    label: "Active Cadets",
    value: 500,
    icon: Users,
    color: "text-blue-600",
    bgColor: "bg-blue-100",
    description: "Dedicated cadets training with us"
  },
  {
    id: 2,
    label: "Events Conducted",
    value: 75,
    icon: Calendar,
    color: "text-green-600",
    bgColor: "bg-green-100",
    description: "Training programs and activities"
  },
  {
    id: 3,
    label: "Awards Won",
    value: 45,
    icon: Trophy,
    color: "text-yellow-600",
    bgColor: "bg-yellow-100",
    description: "Recognition and achievements"
  },
  {
    id: 4,
    label: "Years of Excellence",
    value: 15,
    icon: Award,
    color: "text-purple-600",
    bgColor: "bg-purple-100",
    description: "Serving the institution"
  },
  {
    id: 5,
    label: "Training Hours",
    value: 2500,
    icon: Target,
    color: "text-red-600",
    bgColor: "bg-red-100",
    description: "Comprehensive skill development"
  },
  {
    id: 6,
    label: "Community Projects",
    value: 30,
    icon: Shield,
    color: "text-indigo-600",
    bgColor: "bg-indigo-100",
    description: "Social service initiatives"
  }
]

const achievements = [
  {
    title: "Best NCC Unit Award",
    year: "2023",
    description: "Recognized for outstanding performance in training and discipline"
  },
  {
    title: "State Level Competition",
    year: "2023",
    description: "First place in drill competition at state level"
  },
  {
    title: "Social Service Excellence",
    year: "2022",
    description: "Awarded for exceptional community service initiatives"
  }
]

function AnimatedCounter({ end, duration = 2000, isVisible }: { end: number; duration?: number; isVisible: boolean }) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!isVisible) return

    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      setCount(Math.floor(progress * end))
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [end, duration, isVisible])

  return <span>{count.toLocaleString()}</span>
}

export default function StatsSection() {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Impact in Numbers
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Over the years, we have built a strong foundation of discipline, leadership, and service. 
            Here's a glimpse of our achievements and the impact we've made.
          </p>
        </div>

        {/* Stats grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {stats.map((stat) => {
            const Icon = stat.icon
            return (
              <div
                key={stat.id}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="text-right">
                    <div className={`text-3xl font-bold ${stat.color}`}>
                      <AnimatedCounter end={stat.value} isVisible={isVisible} />
                      {stat.id === 5 && '+'}
                    </div>
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {stat.label}
                </h3>
                <p className="text-sm text-gray-600">
                  {stat.description}
                </p>
              </div>
            )
          })}
        </div>

        {/* Achievements section */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Left side - Achievements list */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Recent Achievements
              </h3>
              <div className="space-y-6">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <Trophy className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-lg font-semibold text-gray-900">
                          {achievement.title}
                        </h4>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                          {achievement.year}
                        </span>
                      </div>
                      <p className="text-gray-600">
                        {achievement.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right side - Call to action */}
            <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">
                Be Part of Our Success Story
              </h3>
              <p className="text-blue-100 mb-6 leading-relaxed">
                Join our community of dedicated cadets and contribute to our legacy of excellence. 
                Experience personal growth, leadership development, and the pride of serving the nation.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                  <span className="text-blue-100">Character Development</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                  <span className="text-blue-100">Leadership Training</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                  <span className="text-blue-100">National Service</span>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-blue-500">
                <a
                  href="/enrollment"
                  className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  Start Your Journey
                  <svg className="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom quote */}
        <div className="mt-12 text-center">
          <blockquote className="text-xl md:text-2xl font-medium text-gray-700 italic">
            "The safety, honour and welfare of your country come first, always and every time. 
            The honour, welfare and comfort of the men you command come next. 
            Your own ease, comfort and safety come last, always and every time."
          </blockquote>
          <cite className="block mt-4 text-lg text-gray-500">
            - Field Marshal Sam Manekshaw
          </cite>
        </div>
      </div>
    </section>
  )
}
