import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/lib/database'
import { Achievement } from '@/lib/models'

// GET /api/achievements - Fetch all achievements with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const year = searchParams.get('year')
    const limit = searchParams.get('limit')
    const search = searchParams.get('search')

    // Build filter object
    const filter: any = {}
    
    if (category && category !== 'all') {
      filter.category = category
    }
    
    if (year && year !== 'all') {
      filter.year = parseInt(year)
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { cadetName: { $regex: search, $options: 'i' } }
      ]
    }

    const achievements = await Database.getAchievements(filter, limit ? parseInt(limit) : undefined)
    
    return NextResponse.json({
      success: true,
      data: achievements,
      count: achievements.length
    })
  } catch (error) {
    console.error('Error fetching achievements:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch achievements' },
      { status: 500 }
    )
  }
}

// POST /api/achievements - Create a new achievement (Admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'cadetName', 'category', 'year', 'date']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Validate category
    const validCategories = ['medal', 'certificate', 'competition', 'camp', 'other']
    if (!validCategories.includes(body.category)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category' },
        { status: 400 }
      )
    }

    // Create achievement object
    const achievementData: Omit<Achievement, '_id'> = {
      title: body.title.trim(),
      description: body.description.trim(),
      cadetName: body.cadetName.trim(),
      cadetRank: body.cadetRank?.trim() || '',
      category: body.category,
      year: parseInt(body.year),
      date: new Date(body.date),
      image: body.image || '',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = await Database.createAchievement(achievementData)
    
    if (result.acknowledged) {
      return NextResponse.json({
        success: true,
        message: 'Achievement created successfully',
        data: { id: result.insertedId }
      }, { status: 201 })
    } else {
      throw new Error('Failed to create achievement')
    }
  } catch (error) {
    console.error('Error creating achievement:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create achievement' },
      { status: 500 }
    )
  }
}

// PUT /api/achievements - Update an achievement (Admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Achievement ID is required' },
        { status: 400 }
      )
    }

    // Remove fields that shouldn't be updated directly
    delete updateData._id
    delete updateData.createdAt
    updateData.updatedAt = new Date()

    // Validate category if provided
    if (updateData.category) {
      const validCategories = ['medal', 'certificate', 'competition', 'camp', 'other']
      if (!validCategories.includes(updateData.category)) {
        return NextResponse.json(
          { success: false, error: 'Invalid category' },
          { status: 400 }
        )
      }
    }

    const result = await Database.updateAchievement(id, updateData)
    
    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Achievement not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Achievement updated successfully'
    })
  } catch (error) {
    console.error('Error updating achievement:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update achievement' },
      { status: 500 }
    )
  }
}

// DELETE /api/achievements - Delete an achievement (Admin only)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Achievement ID is required' },
        { status: 400 }
      )
    }

    const result = await Database.deleteAchievement(id)
    
    if (result.deletedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Achievement not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Achievement deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting achievement:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete achievement' },
      { status: 500 }
    )
  }
}
