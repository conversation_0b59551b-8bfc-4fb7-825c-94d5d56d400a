import clientPromise from './mongodb'
import { 
  Event, 
  Achievement, 
  Enrollment, 
  Notice, 
  Contact, 
  NewsletterSubscription,
  EventRegistration,
  User,
  GalleryImage,
  Statistics
} from './models'
import { ObjectId } from 'mongodb'

const DB_NAME = 'ncc-website'

// Generic database operations
export class Database {
  static async getCollection(collectionName: string) {
    const client = await clientPromise
    const db = client.db(DB_NAME)
    return db.collection(collectionName)
  }

  // Events operations
  static async getEvents(filter: any = {}, limit?: number) {
    const collection = await this.getCollection('events')
    const query = collection.find(filter).sort({ date: -1 })
    return limit ? await query.limit(limit).toArray() : await query.toArray()
  }

  static async getEventById(id: string) {
    const collection = await this.getCollection('events')
    return await collection.findOne({ _id: new ObjectId(id) })
  }

  static async createEvent(event: Omit<Event, '_id'>) {
    const collection = await this.getCollection('events')
    const result = await collection.insertOne({
      ...event,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    return result
  }

  static async updateEvent(id: string, updates: Partial<Event>) {
    const collection = await this.getCollection('events')
    return await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: { ...updates, updatedAt: new Date() } }
    )
  }

  static async deleteEvent(id: string) {
    const collection = await this.getCollection('events')
    return await collection.deleteOne({ _id: new ObjectId(id) })
  }

  // Achievements operations
  static async getAchievements(filter: any = {}, limit?: number) {
    const collection = await this.getCollection('achievements')
    const query = collection.find(filter).sort({ date: -1 })
    return limit ? await query.limit(limit).toArray() : await query.toArray()
  }

  static async createAchievement(achievement: Omit<Achievement, '_id'>) {
    const collection = await this.getCollection('achievements')
    return await collection.insertOne({
      ...achievement,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }

  // Enrollments operations
  static async getEnrollments(filter: any = {}) {
    const collection = await this.getCollection('enrollments')
    return await collection.find(filter).sort({ submittedAt: -1 }).toArray()
  }

  static async createEnrollment(enrollment: Omit<Enrollment, '_id'>) {
    const collection = await this.getCollection('enrollments')
    return await collection.insertOne({
      ...enrollment,
      submittedAt: new Date(),
      status: 'pending'
    })
  }

  static async updateEnrollmentStatus(id: string, status: string, processedBy: string) {
    const collection = await this.getCollection('enrollments')
    return await collection.updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          status, 
          processedAt: new Date(),
          processedBy 
        } 
      }
    )
  }

  // Notices operations
  static async getNotices(filter: any = {}) {
    const collection = await this.getCollection('notices')
    return await collection.find(filter).sort({ publishDate: -1 }).toArray()
  }

  static async getActiveNotices() {
    const collection = await this.getCollection('notices')
    const now = new Date()
    return await collection.find({
      isActive: true,
      publishDate: { $lte: now },
      $or: [
        { expiryDate: { $exists: false } },
        { expiryDate: { $gte: now } }
      ]
    }).sort({ priority: -1, publishDate: -1 }).toArray()
  }

  static async createNotice(notice: Omit<Notice, '_id'>) {
    const collection = await this.getCollection('notices')
    return await collection.insertOne({
      ...notice,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }

  // Contacts operations
  static async getContacts(filter: any = {}) {
    const collection = await this.getCollection('contacts')
    return await collection.find(filter).sort({ submittedAt: -1 }).toArray()
  }

  static async createContact(contact: Omit<Contact, '_id'>) {
    const collection = await this.getCollection('contacts')
    return await collection.insertOne({
      ...contact,
      submittedAt: new Date(),
      status: 'new'
    })
  }

  static async updateContactStatus(id: string, status: string, reply?: string, repliedBy?: string) {
    const collection = await this.getCollection('contacts')
    const updates: any = { status }
    if (reply) {
      updates.reply = reply
      updates.repliedAt = new Date()
      updates.repliedBy = repliedBy
    }
    return await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updates }
    )
  }

  // Newsletter operations
  static async subscribeNewsletter(email: string, name?: string) {
    const collection = await this.getCollection('newsletter')
    const existing = await collection.findOne({ email })
    if (existing) {
      return await collection.updateOne(
        { email },
        { $set: { isActive: true, name } }
      )
    }
    return await collection.insertOne({
      email,
      name,
      isActive: true,
      subscribedAt: new Date()
    })
  }

  // Statistics operations
  static async getStatistics() {
    const eventsCount = await (await this.getCollection('events')).countDocuments()
    const achievementsCount = await (await this.getCollection('achievements')).countDocuments()
    const enrollmentsCount = await (await this.getCollection('enrollments')).countDocuments({ status: 'approved' })
    const pendingEnrollments = await (await this.getCollection('enrollments')).countDocuments({ status: 'pending' })

    return {
      totalEvents: eventsCount,
      totalAchievements: achievementsCount,
      totalCadets: enrollmentsCount,
      pendingEnrollments,
      lastUpdated: new Date()
    }
  }
}
