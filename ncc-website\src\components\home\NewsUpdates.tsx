import Link from 'next/link'
import Image from 'next/image'
import { Calendar, Clock, ArrowR<PERSON>, Bell, Download } from 'lucide-react'
import { formatDate } from '@/lib/utils'

// Mock data - in real app, this would come from the database
const latestNews = [
  {
    id: 1,
    title: "NCC Day Celebration 2024",
    excerpt: "Join us for the grand celebration of NCC Day with parade, cultural programs, and award ceremonies.",
    date: new Date('2024-01-15'),
    category: "Event",
    image: "/news/ncc-day.jpg",
    priority: "high"
  },
  {
    id: 2,
    title: "New Training Module Introduction",
    excerpt: "Advanced leadership training module has been introduced for senior cadets to enhance their command skills.",
    date: new Date('2024-01-10'),
    category: "Training",
    image: "/news/training-module.jpg",
    priority: "medium"
  },
  {
    id: 3,
    title: "Community Service Recognition",
    excerpt: "Our cadets received appreciation for their outstanding contribution to flood relief operations.",
    date: new Date('2024-01-05'),
    category: "Achievement",
    image: "/news/community-service.jpg",
    priority: "medium"
  }
]

const notices = [
  {
    id: 1,
    title: "Enrollment for Summer Camp 2024",
    date: new Date('2024-01-20'),
    priority: "high",
    type: "Enrollment"
  },
  {
    id: 2,
    title: "Uniform Distribution Schedule",
    date: new Date('2024-01-18'),
    priority: "medium",
    type: "General"
  },
  {
    id: 3,
    title: "Medical Examination for New Cadets",
    date: new Date('2024-01-16'),
    priority: "high",
    type: "Medical"
  },
  {
    id: 4,
    title: "Parent-Teacher Meeting Notice",
    date: new Date('2024-01-14'),
    priority: "medium",
    type: "Meeting"
  }
]

const downloads = [
  {
    id: 1,
    title: "NCC Syllabus 2024",
    type: "PDF",
    size: "2.5 MB",
    description: "Complete syllabus for all NCC training programs"
  },
  {
    id: 2,
    title: "Enrollment Application Form",
    type: "PDF",
    size: "1.2 MB",
    description: "Application form for new cadet enrollment"
  },
  {
    id: 3,
    title: "Training Schedule",
    type: "PDF",
    size: "800 KB",
    description: "Weekly training schedule for all units"
  }
]

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800'
    case 'medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'low':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export default function NewsUpdates() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Latest News & Updates
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Stay informed with the latest announcements, news, and important notices from NCC SRMIST Tiruchirappalli.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main news section */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Latest News</h3>
              <Link
                href="/news"
                className="text-blue-600 hover:text-blue-700 font-medium flex items-center"
              >
                View All
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>

            <div className="space-y-6">
              {latestNews.map((news) => (
                <article
                  key={news.id}
                  className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden"
                >
                  <div className="md:flex">
                    <div className="md:w-1/3">
                      <div className="relative h-48 md:h-full">
                        <Image
                          src={news.image}
                          alt={news.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                    <div className="md:w-2/3 p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(news.priority)}`}>
                          {news.priority.charAt(0).toUpperCase() + news.priority.slice(1)}
                        </span>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          {news.category}
                        </span>
                      </div>
                      
                      <h4 className="text-xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors duration-200">
                        <Link href={`/news/${news.id}`}>
                          {news.title}
                        </Link>
                      </h4>
                      
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {news.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>{formatDate(news.date)}</span>
                        </div>
                        <Link
                          href={`/news/${news.id}`}
                          className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center"
                        >
                          Read More
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Link>
                      </div>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Notices */}
            <div className="bg-gray-50 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-xl font-bold text-gray-900 flex items-center">
                  <Bell className="h-5 w-5 mr-2 text-blue-600" />
                  Important Notices
                </h4>
                <Link
                  href="/news/notices"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View All
                </Link>
              </div>
              
              <div className="space-y-3">
                {notices.map((notice) => (
                  <div
                    key={notice.id}
                    className="bg-white rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors duration-200"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-semibold text-gray-900 text-sm leading-tight">
                        {notice.title}
                      </h5>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ml-2 flex-shrink-0 ${getPriorityColor(notice.priority)}`}>
                        {notice.priority}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {notice.type}
                      </span>
                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{formatDate(notice.date)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Downloads */}
            <div className="bg-blue-50 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-xl font-bold text-gray-900 flex items-center">
                  <Download className="h-5 w-5 mr-2 text-blue-600" />
                  Downloads
                </h4>
                <Link
                  href="/news/downloads"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View All
                </Link>
              </div>
              
              <div className="space-y-3">
                {downloads.map((download) => (
                  <div
                    key={download.id}
                    className="bg-white rounded-lg p-4 border border-blue-200 hover:border-blue-400 transition-colors duration-200"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-semibold text-gray-900 text-sm leading-tight">
                        {download.title}
                      </h5>
                      <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded ml-2 flex-shrink-0">
                        {download.type}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">
                      {download.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {download.size}
                      </span>
                      <button className="text-blue-600 hover:text-blue-700 text-xs font-medium flex items-center">
                        Download
                        <Download className="h-3 w-3 ml-1" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Newsletter signup */}
            <div className="bg-gradient-to-br from-orange-500 to-red-600 rounded-xl p-6 text-white">
              <h4 className="text-xl font-bold mb-2">
                Stay Updated
              </h4>
              <p className="text-orange-100 mb-4 text-sm">
                Subscribe to our newsletter for the latest updates and announcements.
              </p>
              <div className="space-y-3">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full px-3 py-2 text-gray-900 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-white"
                />
                <button className="w-full bg-white text-orange-600 px-4 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors duration-200">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
