'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, Clock, User, ArrowRight, Search, Filter, Bell, Download } from 'lucide-react'
import { formatDate, getPriorityColor } from '@/lib/utils'

// Mock data - in real app, this would come from API
const newsArticles = [
  {
    id: 1,
    title: "NCC Day Celebration 2024 - A Grand Success",
    excerpt: "The annual NCC Day celebration at SRMIST Tiruchirappalli was marked by spectacular parades, cultural programs, and award ceremonies recognizing outstanding cadets.",
    content: "The National Cadet Corps Day celebration at SRM Institute of Science and Technology, Tiruchirappalli was a resounding success...",
    date: new Date('2024-01-15'),
    author: "Lt Dr <PERSON>",
    category: "Event",
    image: "/news/ncc-day-2024.jpg",
    priority: "high",
    featured: true
  },
  {
    id: 2,
    title: "New Advanced Leadership Training Module Introduced",
    excerpt: "A comprehensive leadership training module has been introduced for senior cadets to enhance their command and decision-making skills.",
    content: "The new Advanced Leadership Training Module is designed to prepare senior cadets for leadership roles...",
    date: new Date('2024-01-10'),
    author: "Training Department",
    category: "Training",
    image: "/news/leadership-training.jpg",
    priority: "medium",
    featured: false
  },
  {
    id: 3,
    title: "Community Service Recognition - Flood Relief Operations",
    excerpt: "Our cadets received appreciation from the District Collector for their outstanding contribution to recent flood relief operations in the region.",
    content: "The dedication and service of our NCC cadets during the recent flood relief operations has been exemplary...",
    date: new Date('2024-01-05'),
    author: "Social Service Wing",
    category: "Achievement",
    image: "/news/flood-relief.jpg",
    priority: "high",
    featured: true
  },
  {
    id: 4,
    title: "Inter-Battalion Drill Competition Results",
    excerpt: "SRMIST NCC unit secured second position in the state-level inter-battalion drill competition held at Chennai.",
    content: "Our cadets demonstrated exceptional discipline and coordination in the state-level competition...",
    date: new Date('2024-01-02'),
    author: "Competition Team",
    category: "Achievement",
    image: "/news/drill-competition.jpg",
    priority: "medium",
    featured: false
  },
  {
    id: 5,
    title: "Blood Donation Camp - 200+ Units Collected",
    excerpt: "The quarterly blood donation camp organized by NCC cadets collected over 200 units of blood for local hospitals.",
    content: "The blood donation camp was organized in collaboration with Government Hospital, Tiruchirappalli...",
    date: new Date('2023-12-28'),
    author: "Medical Wing",
    category: "Social Service",
    image: "/news/blood-donation.jpg",
    priority: "medium",
    featured: false
  },
  {
    id: 6,
    title: "Adventure Training Camp at Nilgiri Hills",
    excerpt: "50 selected cadets participated in an intensive adventure training camp focusing on trekking, rock climbing, and survival skills.",
    content: "The adventure training camp provided cadets with valuable experience in outdoor activities...",
    date: new Date('2023-12-20'),
    author: "Adventure Wing",
    category: "Training",
    image: "/news/adventure-camp.jpg",
    priority: "low",
    featured: false
  }
]

const categories = [
  { value: 'all', label: 'All News' },
  { value: 'Event', label: 'Events' },
  { value: 'Training', label: 'Training' },
  { value: 'Achievement', label: 'Achievements' },
  { value: 'Social Service', label: 'Social Service' }
]

const quickLinks = [
  { title: 'Important Notices', href: '/news/notices', icon: Bell },
  { title: 'Downloads', href: '/news/downloads', icon: Download },
  { title: 'Event Calendar', href: '/activities/events', icon: Calendar }
]

export default function NewsPage() {
  const [filteredNews, setFilteredNews] = useState(newsArticles)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    let filtered = newsArticles

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        article.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        article.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(article => article.category === selectedCategory)
    }

    setFilteredNews(filtered)
  }, [searchTerm, selectedCategory])

  const featuredNews = newsArticles.filter(article => article.featured)
  const regularNews = filteredNews.filter(article => !article.featured)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              News & Updates
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Stay informed with the latest news, announcements, and updates from NCC SRMIST Tiruchirappalli
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* Quick Links */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickLinks.map((link, index) => {
              const Icon = link.icon
              return (
                <Link
                  key={index}
                  href={link.href}
                  className="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <Icon className="h-6 w-6 text-blue-600 mr-3" />
                  <span className="font-medium text-gray-900">{link.title}</span>
                  <ArrowRight className="h-4 w-4 text-gray-400 ml-auto" />
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search news..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Toggle (Mobile) */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden flex items-center px-4 py-2 bg-white hover:bg-gray-100 rounded-lg transition-colors duration-200 border border-gray-300"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>

            {/* Filters (Desktop) */}
            <div className="hidden lg:flex gap-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Mobile Filters */}
          {showFilters && (
            <div className="lg:hidden mt-4 p-4 bg-white rounded-lg border border-gray-300">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </section>

      {/* Featured News */}
      {featuredNews.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured News</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredNews.map((article) => (
                <article key={article.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-200">
                  <div className="relative h-64">
                    <Image
                      src={article.image}
                      alt={article.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-4 left-4 flex gap-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(article.priority)}`}>
                        {article.priority.charAt(0).toUpperCase() + article.priority.slice(1)}
                      </span>
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {article.category}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-200">
                      <Link href={`/news/${article.id}`}>
                        {article.title}
                      </Link>
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {article.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{formatDate(article.date)}</span>
                      </div>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        <span>{article.author}</span>
                      </div>
                    </div>
                    
                    <Link
                      href={`/news/${article.id}`}
                      className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                    >
                      Read Full Article
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Regular News */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900">
              Latest News
            </h2>
            <span className="text-gray-600">
              {filteredNews.length} article{filteredNews.length !== 1 ? 's' : ''} found
            </span>
          </div>

          {regularNews.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Newspaper className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="space-y-8">
              {regularNews.map((article) => (
                <article key={article.id} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
                  <div className="md:flex">
                    <div className="md:w-1/3">
                      <div className="relative h-48 md:h-full">
                        <Image
                          src={article.image}
                          alt={article.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                    <div className="md:w-2/3 p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(article.priority)}`}>
                          {article.priority.charAt(0).toUpperCase() + article.priority.slice(1)}
                        </span>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          {article.category}
                        </span>
                      </div>
                      
                      <h3 className="text-xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors duration-200">
                        <Link href={`/news/${article.id}`}>
                          {article.title}
                        </Link>
                      </h3>
                      
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {article.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-500 space-x-4">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            <span>{formatDate(article.date)}</span>
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            <span>{article.author}</span>
                          </div>
                        </div>
                        <Link
                          href={`/news/${article.id}`}
                          className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center"
                        >
                          Read More
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Link>
                      </div>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated with NCC News
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Subscribe to our newsletter to receive the latest news, announcements, and updates directly in your inbox.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 rounded-lg focus:ring-2 focus:ring-white focus:outline-none"
            />
            <button className="px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}
