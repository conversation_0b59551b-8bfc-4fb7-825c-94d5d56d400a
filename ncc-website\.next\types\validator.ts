// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/about/ano-message/page.tsx
{
  const handler = {} as typeof import("../../src/app/about/ano-message/page.js")
  handler satisfies AppPageConfig<"/about/ano-message">
}

// Validate ../../src/app/about/mission-vision/page.tsx
{
  const handler = {} as typeof import("../../src/app/about/mission-vision/page.js")
  handler satisfies AppPageConfig<"/about/mission-vision">
}

// Validate ../../src/app/about/page.tsx
{
  const handler = {} as typeof import("../../src/app/about/page.js")
  handler satisfies AppPageConfig<"/about">
}

// Validate ../../src/app/achievements/page.tsx
{
  const handler = {} as typeof import("../../src/app/achievements/page.js")
  handler satisfies AppPageConfig<"/achievements">
}

// Validate ../../src/app/activities/events/page.tsx
{
  const handler = {} as typeof import("../../src/app/activities/events/page.js")
  handler satisfies AppPageConfig<"/activities/events">
}

// Validate ../../src/app/activities/page.tsx
{
  const handler = {} as typeof import("../../src/app/activities/page.js")
  handler satisfies AppPageConfig<"/activities">
}

// Validate ../../src/app/contact/page.tsx
{
  const handler = {} as typeof import("../../src/app/contact/page.js")
  handler satisfies AppPageConfig<"/contact">
}

// Validate ../../src/app/enrollment/page.tsx
{
  const handler = {} as typeof import("../../src/app/enrollment/page.js")
  handler satisfies AppPageConfig<"/enrollment">
}

// Validate ../../src/app/news/page.tsx
{
  const handler = {} as typeof import("../../src/app/news/page.js")
  handler satisfies AppPageConfig<"/news">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/api/achievements/route.ts
{
  const handler = {} as typeof import("../../src/app/api/achievements/route.js")
  handler satisfies RouteHandlerConfig<"/api/achievements">
}

// Validate ../../src/app/api/contact/route.ts
{
  const handler = {} as typeof import("../../src/app/api/contact/route.js")
  handler satisfies RouteHandlerConfig<"/api/contact">
}

// Validate ../../src/app/api/enrollment/route.ts
{
  const handler = {} as typeof import("../../src/app/api/enrollment/route.js")
  handler satisfies RouteHandlerConfig<"/api/enrollment">
}

// Validate ../../src/app/api/events/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/events/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/events/[id]">
}

// Validate ../../src/app/api/events/route.ts
{
  const handler = {} as typeof import("../../src/app/api/events/route.js")
  handler satisfies RouteHandlerConfig<"/api/events">
}





// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
