'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ChevronLeft, ChevronRight, Play } from 'lucide-react'

const heroSlides = [
  {
    id: 1,
    title: "Unity and Discipline",
    subtitle: "National Cadet Corps",
    description: "Building character, leadership, and patriotism among the youth of SRMIST Tiruchirappalli",
    image: "/hero/ncc-parade.jpg",
    cta: "Join NCC",
    ctaLink: "/enrollment"
  },
  {
    id: 2,
    title: "Serve the Nation",
    subtitle: "2 TN (BN) Trichy",
    description: "Developing discipline, leadership qualities, and spirit of adventure through comprehensive training programs",
    image: "/hero/ncc-training.jpg",
    cta: "View Activities",
    ctaLink: "/activities"
  },
  {
    id: 3,
    title: "Excellence in Service",
    subtitle: "SRMIST NCC Unit",
    description: "Fostering secular outlook, comradeship, and ideals of selfless service among cadets",
    image: "/hero/ncc-camp.jpg",
    cta: "Our Achievements",
    ctaLink: "/achievements"
  }
]

export default function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Background slides */}
      <div className="absolute inset-0">
        {heroSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <div className="relative h-full w-full">
              <Image
                src={slide.image}
                alt={slide.title}
                fill
                className="object-cover"
                priority={index === 0}
              />
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/40"></div>
            </div>
          </div>
        ))}
      </div>

      {/* NCC Flag gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-2 ncc-flag-gradient"></div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <div className="text-white space-y-6 animate-fade-in-up">
              <div className="flex items-center space-x-4 mb-4">
                <div className="relative h-16 w-16">
                  <Image
                    src="/ncc-logo.png"
                    alt="NCC Logo"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative h-16 w-16">
                  <Image
                    src="/srmist-logo.png"
                    alt="SRMIST Logo"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
              
              <div>
                <h2 className="text-lg font-medium text-orange-300 mb-2">
                  {heroSlides[currentSlide].subtitle}
                </h2>
                <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
                  {heroSlides[currentSlide].title}
                </h1>
                <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-2xl">
                  {heroSlides[currentSlide].description}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={heroSlides[currentSlide].ctaLink}
                  className="inline-flex items-center px-8 py-4 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-lg transition-colors duration-200 text-center justify-center"
                >
                  {heroSlides[currentSlide].cta}
                </Link>
                <Link
                  href="/about"
                  className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold rounded-lg transition-colors duration-200 text-center justify-center"
                >
                  Learn More
                </Link>
              </div>

              {/* Motto */}
              <div className="pt-8 border-t border-white/20">
                <p className="text-lg font-medium text-orange-300">
                  "Unity and Discipline"
                </p>
                <p className="text-sm text-gray-300">
                  Lt Dr A Arun Kumar - Associate NCC Officer
                </p>
              </div>
            </div>

            {/* Right side - Additional content or stats */}
            <div className="hidden lg:block">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 space-y-6">
                <h3 className="text-2xl font-bold text-white mb-6">Quick Stats</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-300">500+</div>
                    <div className="text-sm text-gray-300">Active Cadets</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-300">50+</div>
                    <div className="text-sm text-gray-300">Events Conducted</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-300">25+</div>
                    <div className="text-sm text-gray-300">Awards Won</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-300">10+</div>
                    <div className="text-sm text-gray-300">Years of Excellence</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex items-center space-x-4">
          {/* Slide indicators */}
          <div className="flex space-x-2">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                  index === currentSlide ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
          
          {/* Auto-play toggle */}
          <button
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
            className="p-2 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
          >
            <Play className={`h-4 w-4 text-white ${isAutoPlaying ? 'opacity-100' : 'opacity-50'}`} />
          </button>
        </div>
      </div>

      {/* Navigation arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
      >
        <ChevronLeft className="h-6 w-6 text-white" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
      >
        <ChevronRight className="h-6 w-6 text-white" />
      </button>
    </section>
  )
}
