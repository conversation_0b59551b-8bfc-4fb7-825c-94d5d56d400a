'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Users, Clock, ArrowRight } from 'lucide-react'
import { formatDate } from '@/lib/utils'

// Mock data - in real app, this would come from the database
const featuredEvents = [
  {
    id: 1,
    title: "Annual Training Camp 2024",
    description: "Comprehensive training program covering drill, weapon training, map reading, and leadership development activities.",
    date: new Date('2024-03-15'),
    location: "NCC Training Ground, SRMIST",
    image: "/events/training-camp.jpg",
    category: "training",
    participants: 150,
    duration: "7 days",
    status: "upcoming"
  },
  {
    id: 2,
    title: "Republic Day Parade Preparation",
    description: "Intensive drill practice and preparation for the Republic Day parade celebration at the institute.",
    date: new Date('2024-01-20'),
    location: "Main Campus Ground",
    image: "/events/republic-day.jpg",
    category: "parade",
    participants: 200,
    duration: "3 weeks",
    status: "ongoing"
  },
  {
    id: 3,
    title: "Community Service Drive",
    description: "Social service initiative focusing on environmental conservation and community development in nearby villages.",
    date: new Date('2024-02-10'),
    location: "Surrounding Villages",
    image: "/events/social-service.jpg",
    category: "social-service",
    participants: 100,
    duration: "2 days",
    status: "upcoming"
  }
]

const upcomingEvents = [
  {
    id: 4,
    title: "Inter-Battalion Competition",
    date: new Date('2024-04-05'),
    location: "Regional NCC Headquarters"
  },
  {
    id: 5,
    title: "Adventure Training Program",
    date: new Date('2024-04-20'),
    location: "Nilgiri Hills"
  },
  {
    id: 6,
    title: "Blood Donation Camp",
    date: new Date('2024-03-25'),
    location: "SRMIST Medical Center"
  }
]

export default function FeaturedEvents() {
  const [activeEvent, setActiveEvent] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveEvent((prev) => (prev + 1) % featuredEvents.length)
    }, 8000)

    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800'
      case 'ongoing':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'training':
        return 'bg-orange-100 text-orange-800'
      case 'parade':
        return 'bg-red-100 text-red-800'
      case 'social-service':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Events & Activities
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Stay updated with our latest training programs, parades, and community service initiatives. 
            Join us in building character and serving the nation.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main featured event */}
          <div className="lg:col-span-2">
            <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="relative h-64 md:h-80">
                <Image
                  src={featuredEvents[activeEvent].image}
                  alt={featuredEvents[activeEvent].title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                
                {/* Status and category badges */}
                <div className="absolute top-4 left-4 flex gap-2">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(featuredEvents[activeEvent].status)}`}>
                    {featuredEvents[activeEvent].status.charAt(0).toUpperCase() + featuredEvents[activeEvent].status.slice(1)}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(featuredEvents[activeEvent].category)}`}>
                    {featuredEvents[activeEvent].category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>

                {/* Event navigation dots */}
                <div className="absolute bottom-4 left-4 flex gap-2">
                  {featuredEvents.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveEvent(index)}
                      className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                        index === activeEvent ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {featuredEvents[activeEvent].title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {featuredEvents[activeEvent].description}
                </p>

                {/* Event details */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{formatDate(featuredEvents[activeEvent].date)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span>{featuredEvents[activeEvent].location}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Users className="h-4 w-4 mr-2" />
                    <span>{featuredEvents[activeEvent].participants} participants</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>{featuredEvents[activeEvent].duration}</span>
                  </div>
                </div>

                <div className="flex gap-3">
                  <Link
                    href={`/activities/events/${featuredEvents[activeEvent].id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 text-center"
                  >
                    View Details
                  </Link>
                  <Link
                    href="/activities/events"
                    className="px-6 py-3 border border-gray-300 hover:border-gray-400 text-gray-700 rounded-lg font-medium transition-colors duration-200 flex items-center"
                  >
                    All Events
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Upcoming events sidebar */}
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-2xl p-6">
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Upcoming Events
              </h4>
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <h5 className="font-semibold text-gray-900 mb-2">
                      {event.title}
                    </h5>
                    <div className="space-y-1 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-2" />
                        <span>{formatDate(event.date)}</span>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-2" />
                        <span>{event.location}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <Link
                href="/activities/events"
                className="block mt-4 text-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                View All Events
              </Link>
            </div>

            {/* Quick action card */}
            <div className="bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl p-6 text-white">
              <h4 className="text-xl font-bold mb-2">
                Join Our Next Event
              </h4>
              <p className="text-orange-100 mb-4">
                Don't miss out on our upcoming training programs and activities.
              </p>
              <Link
                href="/enrollment"
                className="block text-center bg-white text-orange-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
              >
                Register Now
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
