import Link from 'next/link'
import Image from 'next/image'
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react'

const footerLinks = {
  about: [
    { name: 'About NCC', href: '/about' },
    { name: 'Our Battalion', href: '/about/battalion' },
    { name: 'ANO Message', href: '/about/ano-message' },
    { name: 'Mission & Vision', href: '/about/mission-vision' }
  ],
  activities: [
    { name: 'Events', href: '/activities/events' },
    { name: 'Gallery', href: '/activities/gallery' },
    { name: 'Training Programs', href: '/activities/training' },
    { name: 'Achievements', href: '/achievements' }
  ],
  resources: [
    { name: 'Latest News', href: '/news' },
    { name: 'Notices', href: '/news/notices' },
    { name: 'Downloads', href: '/news/downloads' },
    { name: 'Join <PERSON><PERSON>', href: '/enrollment' }
  ],
  support: [
    { name: 'Contact Us', href: '/contact' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' }
  ]
}

const socialLinks = [
  { name: 'Facebook', href: '#', icon: Facebook },
  { name: 'Twitter', href: '#', icon: Twitter },
  { name: 'Instagram', href: '#', icon: Instagram },
  { name: 'YouTube', href: '#', icon: Youtube }
]

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main footer content */}
      <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Logo and description */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="relative h-12 w-12">
                <Image
                  src="/ncc-logo.png"
                  alt="NCC Logo"
                  fill
                  className="object-contain"
                />
              </div>
              <div>
                <h3 className="text-lg font-bold">NCC SRMIST</h3>
                <p className="text-sm text-gray-300">Tiruchirappalli</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              The National Cadet Corps (NCC) at SRM Institute of Science and Technology, 
              Tiruchirappalli, is committed to developing character, comradeship, discipline, 
              leadership, secular outlook, spirit of adventure, and ideals of selfless service 
              among the youth.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-gray-300">
                <MapPin className="h-4 w-4" />
                <span>SRM Institute of Science and Technology, Tiruchirappalli</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-300">
                <Phone className="h-4 w-4" />
                <span>+91 431 2515000</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-300">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* About Links */}
          <div>
            <h3 className="text-sm font-semibold uppercase tracking-wider mb-4">About</h3>
            <ul className="space-y-2">
              {footerLinks.about.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Activities Links */}
          <div>
            <h3 className="text-sm font-semibold uppercase tracking-wider mb-4">Activities</h3>
            <ul className="space-y-2">
              {footerLinks.activities.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h3 className="text-sm font-semibold uppercase tracking-wider mb-4">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social media and newsletter */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex space-x-6 mb-4 md:mb-0">
              {socialLinks.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <span className="sr-only">{item.name}</span>
                    <Icon className="h-5 w-5" />
                  </Link>
                )
              })}
            </div>
            
            {/* Newsletter signup */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-300">Stay updated:</span>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="px-3 py-1 text-sm bg-gray-800 border border-gray-700 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="px-4 py-1 text-sm bg-blue-600 hover:bg-blue-700 rounded-r-md transition-colors">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom bar */}
      <div className="bg-gray-800">
        <div className="mx-auto max-w-7xl px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-400">
            <div className="flex items-center space-x-4 mb-2 md:mb-0">
              <p>&copy; 2024 NCC SRMIST Tiruchirappalli. All rights reserved.</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-xs">Unity and Discipline</span>
              <div className="h-4 w-px bg-gray-600"></div>
              <div className="flex items-center space-x-2">
                <div className="relative h-6 w-6">
                  <Image
                    src="/srmist-logo.png"
                    alt="SRMIST Logo"
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="text-xs">SRMIST Tiruchirappalli</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
