'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Users, Clock, Filter, Search, ArrowRight } from 'lucide-react'
import { formatDate, getStatusColor } from '@/lib/utils'

// Mock data - in real app, this would come from API
const events = [
  {
    id: 1,
    title: "Annual Training Camp 2024",
    description: "Comprehensive training program covering drill, weapon training, map reading, and leadership development activities for all cadets.",
    date: new Date('2024-03-15'),
    location: "NCC Training Ground, SRMIST",
    image: "/events/training-camp.jpg",
    category: "training",
    participants: 150,
    maxParticipants: 200,
    duration: "7 days",
    status: "upcoming",
    registrationOpen: true
  },
  {
    id: 2,
    title: "Republic Day Parade Preparation",
    description: "Intensive drill practice and preparation for the Republic Day parade celebration at the institute.",
    date: new Date('2024-01-20'),
    location: "Main Campus Ground",
    image: "/events/republic-day.jpg",
    category: "parade",
    participants: 200,
    maxParticipants: 250,
    duration: "3 weeks",
    status: "ongoing",
    registrationOpen: false
  },
  {
    id: 3,
    title: "Community Service Drive",
    description: "Social service initiative focusing on environmental conservation and community development in nearby villages.",
    date: new Date('2024-02-10'),
    location: "Surrounding Villages",
    image: "/events/social-service.jpg",
    category: "social-service",
    participants: 100,
    maxParticipants: 150,
    duration: "2 days",
    status: "upcoming",
    registrationOpen: true
  },
  {
    id: 4,
    title: "Inter-Battalion Competition",
    description: "Annual competition between different NCC battalions featuring drill, shooting, and leadership challenges.",
    date: new Date('2024-04-05'),
    location: "Regional NCC Headquarters",
    image: "/events/competition.jpg",
    category: "competition",
    participants: 50,
    maxParticipants: 75,
    duration: "3 days",
    status: "upcoming",
    registrationOpen: true
  },
  {
    id: 5,
    title: "Adventure Training Program",
    description: "Outdoor adventure activities including trekking, rock climbing, and survival skills training.",
    date: new Date('2024-04-20'),
    location: "Nilgiri Hills",
    image: "/events/adventure.jpg",
    category: "adventure",
    participants: 80,
    maxParticipants: 100,
    duration: "5 days",
    status: "upcoming",
    registrationOpen: true
  },
  {
    id: 6,
    title: "Blood Donation Camp",
    description: "Community service initiative to organize blood donation camp in collaboration with local hospitals.",
    date: new Date('2024-03-25'),
    location: "SRMIST Medical Center",
    image: "/events/blood-donation.jpg",
    category: "social-service",
    participants: 120,
    maxParticipants: 200,
    duration: "1 day",
    status: "upcoming",
    registrationOpen: true
  }
]

const categories = [
  { value: 'all', label: 'All Events' },
  { value: 'training', label: 'Training' },
  { value: 'parade', label: 'Parades' },
  { value: 'social-service', label: 'Social Service' },
  { value: 'competition', label: 'Competitions' },
  { value: 'adventure', label: 'Adventure' }
]

const statusFilters = [
  { value: 'all', label: 'All Status' },
  { value: 'upcoming', label: 'Upcoming' },
  { value: 'ongoing', label: 'Ongoing' },
  { value: 'completed', label: 'Completed' }
]

export default function EventsPage() {
  const [filteredEvents, setFilteredEvents] = useState(events)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    let filtered = events

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.location.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(event => event.category === selectedCategory)
    }

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(event => event.status === selectedStatus)
    }

    setFilteredEvents(filtered)
  }, [searchTerm, selectedCategory, selectedStatus])

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'training':
        return 'bg-blue-100 text-blue-800'
      case 'parade':
        return 'bg-red-100 text-red-800'
      case 'social-service':
        return 'bg-green-100 text-green-800'
      case 'competition':
        return 'bg-purple-100 text-purple-800'
      case 'adventure':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Events & Programs
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Discover our comprehensive training programs, exciting activities, and community service opportunities
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Toggle (Mobile) */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>

            {/* Filters (Desktop) */}
            <div className="hidden lg:flex gap-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {statusFilters.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Mobile Filters */}
          {showFilters && (
            <div className="lg:hidden mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {statusFilters.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Events Grid */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredEvents.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Calendar className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No events found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold text-gray-900">
                  {filteredEvents.length} Event{filteredEvents.length !== 1 ? 's' : ''} Found
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredEvents.map((event) => (
                  <div key={event.id} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
                    <div className="relative h-48">
                      <Image
                        src={event.image}
                        alt={event.title}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute top-4 left-4 flex gap-2">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                          {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                        </span>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>
                          {event.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </div>
                      {event.registrationOpen && (
                        <div className="absolute top-4 right-4">
                          <span className="px-3 py-1 bg-green-500 text-white rounded-full text-xs font-medium">
                            Registration Open
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {event.title}
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {event.description}
                      </p>

                      <div className="space-y-2 text-sm text-gray-600 mb-4">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>{formatDate(event.date)}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2" />
                          <span>{event.location}</span>
                        </div>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2" />
                          <span>{event.participants}/{event.maxParticipants} participants</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>{event.duration}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Link
                          href={`/activities/events/${event.id}`}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 text-center text-sm"
                        >
                          View Details
                        </Link>
                        {event.registrationOpen && (
                          <Link
                            href={`/activities/events/${event.id}/register`}
                            className="px-4 py-2 border border-blue-600 text-blue-600 hover:bg-blue-50 rounded-lg font-medium transition-colors duration-200 text-sm"
                          >
                            Register
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Don't Miss Out on Our Events
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto">
            Join us for exciting training programs, community service, and character-building activities.
          </p>
          <Link
            href="/enrollment"
            className="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200"
          >
            Become a Cadet
            <ArrowRight className="h-4 w-4 ml-2" />
          </Link>
        </div>
      </section>
    </div>
  )
}
