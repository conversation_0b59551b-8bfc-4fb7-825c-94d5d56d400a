import Image from 'next/image'
import Link from 'next/link'
import { Quote, Mail, Phone, MapPin, Award, Users, Target } from 'lucide-react'

const achievements = [
  {
    title: "Best NCC Unit Award 2023",
    description: "State level recognition for outstanding performance"
  },
  {
    title: "500+ Cadets Trained",
    description: "Successfully mentored over 500 cadets in leadership"
  },
  {
    title: "15+ Years of Service",
    description: "Dedicated service to NCC and youth development"
  }
]

const initiatives = [
  {
    icon: Users,
    title: "Leadership Development",
    description: "Comprehensive programs to build future leaders"
  },
  {
    icon: Target,
    title: "Skill Enhancement",
    description: "Modern training methods and practical skill development"
  },
  {
    icon: Award,
    title: "Excellence Recognition",
    description: "Acknowledging and rewarding outstanding performance"
  }
]

export default function ANOMessagePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Message from ANO
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Leadership, guidance, and inspiration from our Associate NCC Officer
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* ANO Profile Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Profile Image and Basic Info */}
            <div className="text-center lg:text-left">
              <div className="relative inline-block">
                <div className="relative h-80 w-80 mx-auto lg:mx-0 rounded-2xl overflow-hidden shadow-xl">
                  <Image
                    src="/about/ano-profile.jpg"
                    alt="Lt Dr A Arun Kumar"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="absolute -bottom-4 -right-4 bg-white p-4 rounded-xl shadow-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">ANO</div>
                    <div className="text-sm text-gray-600">Associate NCC Officer</div>
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  Lt Dr A Arun Kumar
                </h2>
                <p className="text-xl text-blue-600 font-semibold mb-4">
                  Associate NCC Officer
                </p>
                <p className="text-lg text-gray-600 mb-6">
                  2 Tamil Nadu (BN) Trichy, NCC SRMIST Tiruchirappalli
                </p>
                
                {/* Contact Information */}
                <div className="space-y-3 text-left">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <span className="text-gray-700"><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-blue-600" />
                    <span className="text-gray-700">+91 431 2515000 (Ext: 2345)</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-blue-600" />
                    <span className="text-gray-700">NCC Office, SRMIST Tiruchirappalli</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Achievements and Recognition */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Leadership & Achievements
              </h3>
              <div className="space-y-6">
                {achievements.map((achievement, index) => (
                  <div key={index} className="bg-gray-50 rounded-xl p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {achievement.title}
                    </h4>
                    <p className="text-gray-600">
                      {achievement.description}
                    </p>
                  </div>
                ))}
              </div>
              
              <div className="mt-8 p-6 bg-blue-50 rounded-xl">
                <h4 className="text-lg font-semibold text-blue-900 mb-3">
                  Educational Background
                </h4>
                <ul className="space-y-2 text-blue-800">
                  <li>• Ph.D. in Management Studies</li>
                  <li>• M.B.A. in Human Resource Management</li>
                  <li>• B.E. in Mechanical Engineering</li>
                  <li>• NCC 'C' Certificate Holder</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ANO Message */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Quote className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                A Message from Our ANO
              </h2>
            </div>
            
            <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
              <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                <p className="text-xl mb-6 font-medium text-gray-900">
                  Dear Cadets, Faculty, and Esteemed Visitors,
                </p>
                
                <p className="mb-6">
                  It gives me immense pleasure to welcome you to the NCC unit of SRM Institute of Science and Technology, Tiruchirappalli. As the Associate NCC Officer, I am privileged to lead and guide our exceptional cadets who embody the true spirit of "Unity and Discipline."
                </p>
                
                <p className="mb-6">
                  The National Cadet Corps has always been a cornerstone in shaping the character and personality of our youth. At SRMIST Tiruchirappalli, we are committed to providing comprehensive training that goes beyond traditional academic learning. Our NCC program is designed to develop leadership qualities, instill discipline, foster patriotism, and create responsible citizens who will contribute meaningfully to our nation's progress.
                </p>
                
                <p className="mb-6">
                  Over the years, our unit has grown from strength to strength, with over 500 dedicated cadets participating in various training programs, camps, parades, and social service activities. We take pride in our achievements at state and national levels, but more importantly, we celebrate the personal growth and transformation of each cadet who passes through our program.
                </p>
                
                <p className="mb-6">
                  Our training methodology combines traditional military discipline with modern educational approaches. We focus on developing critical thinking, problem-solving abilities, and leadership skills that will serve our cadets well in their future endeavors, whether in military service, corporate leadership, or public service.
                </p>
                
                <p className="mb-6">
                  I encourage all eligible students to consider joining our NCC family. The experiences, friendships, and lessons learned here will stay with you for a lifetime. Together, we will continue to uphold the highest standards of excellence and contribute to building a stronger, more unified nation.
                </p>
                
                <p className="text-xl font-semibold text-gray-900 mb-2">
                  Jai Hind!
                </p>
                
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="flex items-center space-x-4">
                    <div className="relative h-16 w-16 rounded-full overflow-hidden">
                      <Image
                        src="/about/ano-signature.jpg"
                        alt="ANO Signature"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">Lt Dr A Arun Kumar</p>
                      <p className="text-gray-600">Associate NCC Officer</p>
                      <p className="text-sm text-gray-500">2 TN (BN) Trichy, SRMIST Tiruchirappalli</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Vision and Initiatives */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Vision & Initiatives
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Under the leadership of our ANO, we continue to innovate and excel in cadet development
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {initiatives.map((initiative, index) => {
              const Icon = initiative.icon
              return (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-4">
                    <div className="p-4 bg-blue-100 rounded-full">
                      <Icon className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {initiative.title}
                  </h3>
                  <p className="text-gray-600">
                    {initiative.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Join Our NCC Family
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Experience the transformative journey of leadership, discipline, and service under expert guidance.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/enrollment"
              className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Apply for Enrollment
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold rounded-lg transition-colors duration-200"
            >
              Contact ANO Office
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
