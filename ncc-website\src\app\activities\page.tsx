import Link from 'next/link'
import Image from 'next/image'
import { Calendar, Users, MapPin, Clock, ArrowRight, Target, Shield, Heart } from 'lucide-react'

const activityCategories = [
  {
    id: 'training',
    title: 'Training Programs',
    description: 'Comprehensive military training including drill, weapon training, map reading, and field exercises',
    icon: Target,
    color: 'bg-blue-500',
    hoverColor: 'hover:bg-blue-600',
    image: '/activities/training.jpg',
    activities: ['Basic Military Training', 'Advanced Leadership Course', 'Weapon Training', 'Map Reading & Navigation']
  },
  {
    id: 'camps',
    title: 'Camps & Expeditions',
    description: 'Annual training camps, adventure activities, and expedition programs for character building',
    icon: Shield,
    color: 'bg-green-500',
    hoverColor: 'hover:bg-green-600',
    image: '/activities/camps.jpg',
    activities: ['Annual Training Camp', 'Adventure Camp', 'Trekking Expeditions', 'Rock Climbing']
  },
  {
    id: 'parades',
    title: 'Parades & Ceremonies',
    description: 'Republic Day parades, ceremonial events, and formal military ceremonies',
    icon: Users,
    color: 'bg-red-500',
    hoverColor: 'hover:bg-red-600',
    image: '/activities/parades.jpg',
    activities: ['Republic Day Parade', 'Independence Day Ceremony', 'Passing Out Parade', 'Guard of Honor']
  },
  {
    id: 'social-service',
    title: 'Social Service',
    description: 'Community service initiatives, disaster relief, and social welfare programs',
    icon: Heart,
    color: 'bg-purple-500',
    hoverColor: 'hover:bg-purple-600',
    image: '/activities/social-service.jpg',
    activities: ['Blood Donation Drives', 'Environmental Conservation', 'Disaster Relief', 'Community Development']
  }
]

const upcomingEvents = [
  {
    id: 1,
    title: 'Annual Training Camp 2024',
    date: '2024-03-15',
    location: 'NCC Training Ground',
    participants: 150,
    category: 'training',
    image: '/events/training-camp.jpg'
  },
  {
    id: 2,
    title: 'Republic Day Parade',
    date: '2024-01-26',
    location: 'Main Campus Ground',
    participants: 200,
    category: 'parades',
    image: '/events/republic-day.jpg'
  },
  {
    id: 3,
    title: 'Community Service Drive',
    date: '2024-02-10',
    location: 'Local Villages',
    participants: 100,
    category: 'social-service',
    image: '/events/social-service.jpg'
  }
]

const stats = [
  { label: 'Events This Year', value: '75+' },
  { label: 'Active Participants', value: '500+' },
  { label: 'Training Hours', value: '2500+' },
  { label: 'Community Projects', value: '30+' }
]

export default function ActivitiesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Activities & Events
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Comprehensive training programs, exciting adventures, and meaningful community service opportunities
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Activity Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Activity Categories
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Explore our diverse range of activities designed to develop character, leadership, and service
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {activityCategories.map((category) => {
              const Icon = category.icon
              return (
                <div key={category.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="relative h-48">
                    <Image
                      src={category.image}
                      alt={category.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black/40"></div>
                    <div className="absolute top-4 left-4">
                      <div className={`p-3 rounded-lg ${category.color}`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">
                      {category.title}
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {category.description}
                    </p>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Key Activities:</h4>
                      <ul className="space-y-1">
                        {category.activities.map((activity, index) => (
                          <li key={index} className="text-sm text-gray-600 flex items-center">
                            <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                            {activity}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <Link
                      href={`/activities/${category.id}`}
                      className={`inline-flex items-center px-6 py-3 ${category.color} ${category.hoverColor} text-white font-semibold rounded-lg transition-colors duration-200`}
                    >
                      Explore {category.title}
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Link>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Upcoming Events */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Upcoming Events
              </h2>
              <p className="text-lg text-gray-600">
                Don't miss these exciting upcoming activities and events
              </p>
            </div>
            <Link
              href="/activities/events"
              className="hidden md:inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200"
            >
              View All Events
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src={event.image}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-xs font-medium">
                      {event.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {event.title}
                  </h3>
                  
                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{new Date(event.date).toLocaleDateString('en-IN', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{event.location}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2" />
                      <span>{event.participants} participants</span>
                    </div>
                  </div>
                  
                  <Link
                    href={`/activities/events/${event.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Learn More
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 text-center md:hidden">
            <Link
              href="/activities/events"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200"
            >
              View All Events
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Join Our Activities?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Participate in our comprehensive training programs and make a difference in your community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/enrollment"
              className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Enroll Now
            </Link>
            <Link
              href="/activities/events"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold rounded-lg transition-colors duration-200"
            >
              View All Events
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
