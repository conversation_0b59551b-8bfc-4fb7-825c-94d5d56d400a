import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/lib/database'
import { Contact } from '@/lib/models'
import nodemailer from 'nodemailer'

// Email configuration
const transporter = nodemailer.createTransporter({
  host: process.env.EMAIL_SERVER_HOST,
  port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
})

// GET /api/contact - Fetch all contact messages (Admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')

    const filter: any = {}
    if (status && status !== 'all') {
      filter.status = status
    }

    const contacts = await Database.getContacts(filter)
    
    return NextResponse.json({
      success: true,
      data: contacts,
      count: contacts.length
    })
  } catch (error) {
    console.error('Error fetching contacts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch contact messages' },
      { status: 500 }
    )
  }
}

// POST /api/contact - Submit new contact message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ['name', 'email', 'subject', 'message']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate phone number if provided
    if (body.phone) {
      const phoneRegex = /^[6-9]\d{9}$/
      if (!phoneRegex.test(body.phone.replace(/\s+/g, ''))) {
        return NextResponse.json(
          { success: false, error: 'Invalid phone number format' },
          { status: 400 }
        )
      }
    }

    // Create contact object
    const contactData: Omit<Contact, '_id'> = {
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      phone: body.phone?.replace(/\s+/g, '') || '',
      subject: body.subject.trim(),
      message: body.message.trim(),
      status: 'new',
      submittedAt: new Date()
    }

    const result = await Database.createContact(contactData)
    
    if (result.acknowledged) {
      // Send confirmation email to sender
      try {
        await sendConfirmationEmail(contactData)
      } catch (emailError) {
        console.error('Error sending confirmation email:', emailError)
        // Don't fail the contact submission if email fails
      }

      // Send notification email to admin
      try {
        await sendAdminNotification(contactData)
      } catch (emailError) {
        console.error('Error sending admin notification:', emailError)
      }

      return NextResponse.json({
        success: true,
        message: 'Message sent successfully',
        data: { id: result.insertedId }
      }, { status: 201 })
    } else {
      throw new Error('Failed to create contact message')
    }
  } catch (error) {
    console.error('Error creating contact:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send message' },
      { status: 500 }
    )
  }
}

// PUT /api/contact - Update contact message status (Admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, status, reply, repliedBy } = body
    
    if (!id || !status) {
      return NextResponse.json(
        { success: false, error: 'Contact ID and status are required' },
        { status: 400 }
      )
    }

    if (!['new', 'read', 'replied', 'closed'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status value' },
        { status: 400 }
      )
    }

    const result = await Database.updateContactStatus(id, status, reply, repliedBy)
    
    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Contact message not found' },
        { status: 404 }
      )
    }

    // Send reply email if reply is provided
    if (reply && status === 'replied') {
      try {
        const contacts = await Database.getContacts({ _id: id })
        if (contacts.length > 0) {
          await sendReplyEmail(contacts[0], reply, repliedBy || 'NCC Team')
        }
      } catch (emailError) {
        console.error('Error sending reply email:', emailError)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Contact status updated successfully'
    })
  } catch (error) {
    console.error('Error updating contact:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update contact status' },
      { status: 500 }
    )
  }
}

// Helper function to send confirmation email
async function sendConfirmationEmail(contact: Omit<Contact, '_id'>) {
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: contact.email,
    subject: 'Message Received - NCC SRMIST Tiruchirappalli',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 20px; text-align: center;">
          <h1>NCC SRMIST Tiruchirappalli</h1>
          <p>Unity and Discipline</p>
        </div>
        
        <div style="padding: 20px; background: #f9fafb;">
          <h2 style="color: #1e40af;">Thank You for Contacting Us!</h2>
          
          <p>Dear ${contact.name},</p>
          
          <p>We have received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24-48 hours.</p>
          
          <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin-top: 0;">Your Message Details:</h3>
            <p><strong>Subject:</strong> ${contact.subject}</p>
            <p><strong>Message:</strong> ${contact.message}</p>
            <p><strong>Submitted:</strong> ${contact.submittedAt.toLocaleDateString()}</p>
          </div>
          
          <p>If you have any urgent queries, please feel free to call us at +91 431 2515000 or visit our office during working hours.</p>
          
          <p>Best regards,<br>
          <strong>NCC Team</strong><br>
          SRMIST Tiruchirappalli</p>
        </div>
        
        <div style="background: #1e40af; color: white; padding: 10px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 NCC SRMIST Tiruchirappalli. All rights reserved.</p>
        </div>
      </div>
    `
  }

  await transporter.sendMail(mailOptions)
}

// Helper function to send admin notification
async function sendAdminNotification(contact: Omit<Contact, '_id'>) {
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: process.env.ADMIN_EMAIL,
    subject: 'New Contact Message - NCC SRMIST Tiruchirappalli',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1e40af;">New Contact Message Received</h2>
        
        <div style="background: #f9fafb; padding: 15px; border-radius: 8px;">
          <h3>Contact Details:</h3>
          <p><strong>Name:</strong> ${contact.name}</p>
          <p><strong>Email:</strong> ${contact.email}</p>
          ${contact.phone ? `<p><strong>Phone:</strong> ${contact.phone}</p>` : ''}
          <p><strong>Subject:</strong> ${contact.subject}</p>
          <p><strong>Submitted:</strong> ${contact.submittedAt.toLocaleDateString()}</p>
        </div>
        
        <div style="background: #fff; padding: 15px; border-radius: 8px; margin-top: 15px;">
          <h3>Message:</h3>
          <p>${contact.message}</p>
        </div>
        
        <p>Please respond to this inquiry through the admin dashboard.</p>
      </div>
    `
  }

  await transporter.sendMail(mailOptions)
}

// Helper function to send reply email
async function sendReplyEmail(contact: any, reply: string, repliedBy: string) {
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: contact.email,
    subject: `Re: ${contact.subject}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 20px; text-align: center;">
          <h1>NCC SRMIST Tiruchirappalli</h1>
        </div>
        
        <div style="padding: 20px;">
          <h2 style="color: #1e40af;">Response to Your Inquiry</h2>
          
          <p>Dear ${contact.name},</p>
          
          <p>Thank you for contacting us. Here is our response to your inquiry:</p>
          
          <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>Your Original Message:</h3>
            <p><strong>Subject:</strong> ${contact.subject}</p>
            <p>${contact.message}</p>
          </div>
          
          <div style="background: #e0f2fe; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>Our Response:</h3>
            <p>${reply}</p>
          </div>
          
          <p>If you have any further questions, please don't hesitate to contact us.</p>
          
          <p>Best regards,<br>
          <strong>${repliedBy}</strong><br>
          NCC SRMIST Tiruchirappalli</p>
        </div>
        
        <div style="background: #1e40af; color: white; padding: 10px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 NCC SRMIST Tiruchirappalli. All rights reserved.</p>
        </div>
      </div>
    `
  }

  await transporter.sendMail(mailOptions)
}
