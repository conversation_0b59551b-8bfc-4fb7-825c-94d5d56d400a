import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string): string {
  const d = new Date(date)
  return d.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function formatDateTime(date: Date | string): string {
  const d = new Date(date)
  return d.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^[6-9]\d{9}$/
  return phoneRegex.test(phone.replace(/\s+/g, ''))
}

export function getEventStatus(eventDate: Date): 'upcoming' | 'ongoing' | 'completed' {
  const now = new Date()
  const event = new Date(eventDate)
  const daysDiff = Math.ceil((event.getTime() - now.getTime()) / (1000 * 3600 * 24))
  
  if (daysDiff > 0) return 'upcoming'
  if (daysDiff === 0) return 'ongoing'
  return 'completed'
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'upcoming':
    case 'pending':
    case 'new':
      return 'bg-yellow-100 text-yellow-800'
    case 'ongoing':
    case 'approved':
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'completed':
    case 'read':
    case 'replied':
      return 'bg-blue-100 text-blue-800'
    case 'cancelled':
    case 'rejected':
    case 'closed':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export function getPriorityColor(priority: string): string {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800'
    case 'medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'low':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// NCC specific utilities
export const NCC_COLORS = {
  saffron: '#FF9933',
  white: '#FFFFFF',
  green: '#138808',
  navy: '#000080'
}

export const NCC_RANKS = [
  'Cadet',
  'Lance Corporal',
  'Corporal',
  'Sergeant',
  'Company Sergeant Major',
  'Regimental Sergeant Major',
  'Academy Sergeant Major',
  'Cadet Under Officer'
]

export const DEPARTMENTS = [
  'Computer Science and Engineering',
  'Information Technology',
  'Electronics and Communication Engineering',
  'Electrical and Electronics Engineering',
  'Mechanical Engineering',
  'Civil Engineering',
  'Chemical Engineering',
  'Biotechnology',
  'Biomedical Engineering',
  'Aerospace Engineering',
  'Automobile Engineering',
  'Marine Engineering',
  'Management Studies',
  'Commerce',
  'Science and Humanities',
  'Architecture',
  'Hotel Management',
  'Other'
]

export const ACADEMIC_YEARS = [
  { value: 1, label: '1st Year' },
  { value: 2, label: '2nd Year' },
  { value: 3, label: '3rd Year' },
  { value: 4, label: '4th Year' },
  { value: 5, label: '5th Year' }
]
