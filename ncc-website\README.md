# NCC SRMIST Tiruchirappalli Website

A comprehensive, modern website for the National Cadet Corps (NCC) unit at SRM Institute of Science and Technology, Tiruchirappalli. Built with Next.js, Tailwind CSS, and MongoDB Atlas.

## 🌟 Features

### Core Pages
- **Homepage**: Hero section with NCC emblem, dynamic banner, quick navigation cards
- **About Us**: NCC history, battalion details, ANO message, mission & vision
- **Activities & Events**: Dynamic events listing with CRUD operations and gallery
- **Achievements**: Showcase cadet accomplishments with filtering and search
- **Enrollment**: Student registration form with validation and email confirmation
- **News & Updates**: Dynamic news feed with notices and downloadable resources
- **Contact**: Contact form, Google Maps integration, and team information

### Technical Features
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Modern UI/UX**: Clean, professional design reflecting NCC values
- **Database Integration**: MongoDB Atlas with comprehensive schemas
- **API Routes**: RESTful APIs for all CRUD operations
- **Form Validation**: Client and server-side validation with Zod
- **Email Integration**: Automated emails using Nodemailer
- **SEO Optimized**: Meta tags, OpenGraph, sitemap, robots.txt
- **Performance**: Optimized images with Next.js Image component

## 🚀 Tech Stack

- **Frontend**: Next.js 15 (App Router), TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB Atlas
- **Authentication**: NextAuth.js (for admin)
- **Email**: Nodemailer
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Lucide React icons, React Hot Toast
- **Animations**: Framer Motion

## 📋 Prerequisites

- Node.js 18+ and npm
- MongoDB Atlas account
- Email service (Gmail/SMTP) for notifications
- Google Maps API key (optional)

## 🛠️ Installation & Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
Update the `.env.local` file with your credentials:

```env
# MongoDB Atlas Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/ncc-website?retryWrites=true&w=majority

# Email Configuration (Nodemailer)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
```

### 3. Run Development Server
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the website.

## 📁 Project Structure

```
ncc-website/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── about/             # About pages
│   │   ├── activities/        # Activities and events
│   │   ├── achievements/      # Achievements showcase
│   │   ├── api/              # API routes
│   │   ├── contact/          # Contact page
│   │   ├── enrollment/       # Enrollment form
│   │   ├── news/             # News and updates
│   │   └── globals.css       # Global styles
│   ├── components/           # Reusable components
│   │   ├── home/            # Homepage components
│   │   └── layout/          # Layout components
│   └── lib/                 # Utilities and configurations
├── public/                  # Static assets
└── package.json
```

## 🚀 Deployment

### Vercel Deployment
1. Push code to GitHub repository
2. Connect repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

## 📧 Email Configuration

### Gmail Setup
1. Enable 2-factor authentication
2. Generate app-specific password
3. Use app password in `EMAIL_SERVER_PASSWORD`

## 🎨 Customization

### Colors & Branding
- NCC colors are defined in `globals.css`
- Update logos in `/public` directory
- Modify color scheme in Tailwind config

## 📱 Mobile Responsiveness

- Mobile-first design approach
- Responsive navigation with hamburger menu
- Touch-friendly interface elements
- Optimized images for different screen sizes

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Phone: +91 431 2515000

---

**Unity and Discipline** - NCC SRMIST Tiruchirappalli
