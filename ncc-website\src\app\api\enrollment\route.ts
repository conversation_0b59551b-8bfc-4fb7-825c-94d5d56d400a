import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/lib/database'
import { Enrollment } from '@/lib/models'
import nodemailer from 'nodemailer'

// Email configuration
const transporter = nodemailer.createTransporter({
  host: process.env.EMAIL_SERVER_HOST,
  port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
})

// GET /api/enrollment - Fetch all enrollments (Admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')

    const filter: any = {}
    if (status && status !== 'all') {
      filter.status = status
    }

    const enrollments = await Database.getEnrollments(filter)
    
    return NextResponse.json({
      success: true,
      data: enrollments,
      count: enrollments.length
    })
  } catch (error) {
    console.error('Error fetching enrollments:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch enrollments' },
      { status: 500 }
    )
  }
}

// POST /api/enrollment - Submit new enrollment application
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = [
      'name', 'email', 'phone', 'rollNumber', 'department', 
      'year', 'dateOfBirth', 'address', 'parentName', 
      'parentPhone', 'reasonForJoining', 'agreeToTerms'
    ]
    
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate phone numbers
    const phoneRegex = /^[6-9]\d{9}$/
    if (!phoneRegex.test(body.phone.replace(/\s+/g, ''))) {
      return NextResponse.json(
        { success: false, error: 'Invalid phone number format' },
        { status: 400 }
      )
    }

    if (!phoneRegex.test(body.parentPhone.replace(/\s+/g, ''))) {
      return NextResponse.json(
        { success: false, error: 'Invalid parent phone number format' },
        { status: 400 }
      )
    }

    // Check if user already applied
    const existingEnrollment = await Database.getEnrollments({ 
      $or: [
        { email: body.email },
        { rollNumber: body.rollNumber }
      ]
    })

    if (existingEnrollment.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Application already exists with this email or roll number' },
        { status: 400 }
      )
    }

    // Create enrollment object
    const enrollmentData: Omit<Enrollment, '_id'> = {
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      phone: body.phone.replace(/\s+/g, ''),
      department: body.department,
      year: parseInt(body.year),
      rollNumber: body.rollNumber.trim().toUpperCase(),
      reasonForJoining: body.reasonForJoining.trim(),
      previousExperience: body.previousExperience?.trim() || '',
      status: 'pending',
      submittedAt: new Date()
    }

    // Add additional fields
    enrollmentData.dateOfBirth = new Date(body.dateOfBirth)
    enrollmentData.address = body.address.trim()
    enrollmentData.parentName = body.parentName.trim()
    enrollmentData.parentPhone = body.parentPhone.replace(/\s+/g, '')
    enrollmentData.medicalConditions = body.medicalConditions?.trim() || ''

    const result = await Database.createEnrollment(enrollmentData)
    
    if (result.acknowledged) {
      // Send confirmation email to applicant
      try {
        await sendConfirmationEmail(enrollmentData)
      } catch (emailError) {
        console.error('Error sending confirmation email:', emailError)
        // Don't fail the enrollment if email fails
      }

      // Send notification email to admin
      try {
        await sendAdminNotification(enrollmentData)
      } catch (emailError) {
        console.error('Error sending admin notification:', emailError)
      }

      return NextResponse.json({
        success: true,
        message: 'Enrollment application submitted successfully',
        data: { id: result.insertedId }
      }, { status: 201 })
    } else {
      throw new Error('Failed to create enrollment')
    }
  } catch (error) {
    console.error('Error creating enrollment:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to submit enrollment application' },
      { status: 500 }
    )
  }
}

// PUT /api/enrollment - Update enrollment status (Admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, status, processedBy } = body
    
    if (!id || !status) {
      return NextResponse.json(
        { success: false, error: 'Enrollment ID and status are required' },
        { status: 400 }
      )
    }

    if (!['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status value' },
        { status: 400 }
      )
    }

    const result = await Database.updateEnrollmentStatus(id, status, processedBy || 'Admin')
    
    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Enrollment not found' },
        { status: 404 }
      )
    }

    // Send status update email to applicant
    try {
      const enrollment = await Database.getEnrollments({ _id: id })
      if (enrollment.length > 0) {
        await sendStatusUpdateEmail(enrollment[0], status)
      }
    } catch (emailError) {
      console.error('Error sending status update email:', emailError)
    }

    return NextResponse.json({
      success: true,
      message: 'Enrollment status updated successfully'
    })
  } catch (error) {
    console.error('Error updating enrollment:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update enrollment status' },
      { status: 500 }
    )
  }
}

// Helper function to send confirmation email
async function sendConfirmationEmail(enrollment: Omit<Enrollment, '_id'>) {
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: enrollment.email,
    subject: 'NCC Enrollment Application Received - SRMIST Tiruchirappalli',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 20px; text-align: center;">
          <h1>NCC SRMIST Tiruchirappalli</h1>
          <p>Unity and Discipline</p>
        </div>
        
        <div style="padding: 20px; background: #f9fafb;">
          <h2 style="color: #1e40af;">Application Received Successfully!</h2>
          
          <p>Dear ${enrollment.name},</p>
          
          <p>Thank you for your interest in joining the National Cadet Corps at SRMIST Tiruchirappalli. We have successfully received your enrollment application.</p>
          
          <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin-top: 0;">Application Details:</h3>
            <p><strong>Name:</strong> ${enrollment.name}</p>
            <p><strong>Roll Number:</strong> ${enrollment.rollNumber}</p>
            <p><strong>Department:</strong> ${enrollment.department}</p>
            <p><strong>Year:</strong> ${enrollment.year}</p>
            <p><strong>Submitted:</strong> ${enrollment.submittedAt.toLocaleDateString()}</p>
          </div>
          
          <h3 style="color: #1e40af;">What's Next?</h3>
          <ul>
            <li>Application review (2-3 business days)</li>
            <li>Medical examination (if selected)</li>
            <li>Interview with NCC officers</li>
            <li>Final selection notification</li>
          </ul>
          
          <p>We will contact you via email or phone regarding the status of your application. Please ensure your contact details are up to date.</p>
          
          <p>For any queries, please contact us at:</p>
          <p>Email: <EMAIL><br>
          Phone: +91 431 2515000</p>
          
          <p>Best regards,<br>
          <strong>Lt Dr A Arun Kumar</strong><br>
          Associate NCC Officer<br>
          SRMIST Tiruchirappalli</p>
        </div>
        
        <div style="background: #1e40af; color: white; padding: 10px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 NCC SRMIST Tiruchirappalli. All rights reserved.</p>
        </div>
      </div>
    `
  }

  await transporter.sendMail(mailOptions)
}

// Helper function to send admin notification
async function sendAdminNotification(enrollment: Omit<Enrollment, '_id'>) {
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: process.env.ADMIN_EMAIL,
    subject: 'New NCC Enrollment Application - SRMIST Tiruchirappalli',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1e40af;">New NCC Enrollment Application</h2>
        
        <div style="background: #f9fafb; padding: 15px; border-radius: 8px;">
          <h3>Applicant Details:</h3>
          <p><strong>Name:</strong> ${enrollment.name}</p>
          <p><strong>Email:</strong> ${enrollment.email}</p>
          <p><strong>Phone:</strong> ${enrollment.phone}</p>
          <p><strong>Roll Number:</strong> ${enrollment.rollNumber}</p>
          <p><strong>Department:</strong> ${enrollment.department}</p>
          <p><strong>Year:</strong> ${enrollment.year}</p>
          <p><strong>Submitted:</strong> ${enrollment.submittedAt.toLocaleDateString()}</p>
        </div>
        
        <div style="background: #fff; padding: 15px; border-radius: 8px; margin-top: 15px;">
          <h3>Reason for Joining:</h3>
          <p>${enrollment.reasonForJoining}</p>
        </div>
        
        <p>Please review the application in the admin dashboard.</p>
      </div>
    `
  }

  await transporter.sendMail(mailOptions)
}

// Helper function to send status update email
async function sendStatusUpdateEmail(enrollment: any, status: string) {
  const statusMessages = {
    approved: {
      subject: 'Congratulations! Your NCC Application has been Approved',
      message: 'We are pleased to inform you that your application to join NCC has been approved. Welcome to the NCC family!',
      nextSteps: [
        'Attend the orientation session',
        'Complete medical examination',
        'Collect your NCC uniform',
        'Begin regular training sessions'
      ]
    },
    rejected: {
      subject: 'NCC Application Status Update',
      message: 'Thank you for your interest in joining NCC. Unfortunately, we are unable to accept your application at this time.',
      nextSteps: [
        'You may reapply in the next enrollment cycle',
        'Focus on improving your academic performance',
        'Participate in other extracurricular activities'
      ]
    }
  }

  const statusInfo = statusMessages[status as keyof typeof statusMessages]
  if (!statusInfo) return

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: enrollment.email,
    subject: statusInfo.subject,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 20px; text-align: center;">
          <h1>NCC SRMIST Tiruchirappalli</h1>
        </div>
        
        <div style="padding: 20px;">
          <h2 style="color: ${status === 'approved' ? '#059669' : '#dc2626'};">
            Application ${status.charAt(0).toUpperCase() + status.slice(1)}
          </h2>
          
          <p>Dear ${enrollment.name},</p>
          
          <p>${statusInfo.message}</p>
          
          <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>Next Steps:</h3>
            <ul>
              ${statusInfo.nextSteps.map(step => `<li>${step}</li>`).join('')}
            </ul>
          </div>
          
          <p>For any queries, please contact <NAME_EMAIL></p>
          
          <p>Best regards,<br>
          NCC SRMIST Tiruchirappalli</p>
        </div>
      </div>
    `
  }

  await transporter.sendMail(mailOptions)
}
