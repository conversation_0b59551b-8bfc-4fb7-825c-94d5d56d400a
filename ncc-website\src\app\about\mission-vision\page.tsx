import Image from 'next/image'
import Link from 'next/link'
import { Target, Eye, Heart, Shield, Users, Star, Award, Compass } from 'lucide-react'

const missionPoints = [
  {
    icon: Shield,
    title: "Character Development",
    description: "To develop character, comradeship, discipline, leadership, secular outlook, spirit of adventure and ideals of selfless service among the youth of the country."
  },
  {
    icon: Users,
    title: "Unity & Integration",
    description: "To create a human resource of organized, trained and motivated youth to provide leadership in all walks of life and be always available for the service of the nation."
  },
  {
    icon: Star,
    title: "National Service",
    description: "To provide a suitable environment to motivate the youth to take up a career in the armed forces and contribute to national security and development."
  }
]

const visionElements = [
  {
    icon: Target,
    title: "Excellence in Training",
    description: "To be recognized as the premier NCC unit in Tamil Nadu, setting benchmarks in cadet training and development."
  },
  {
    icon: Award,
    title: "Leadership Development",
    description: "To produce confident, disciplined, and patriotic leaders who will serve the nation with distinction."
  },
  {
    icon: Heart,
    title: "Social Responsibility",
    description: "To foster a culture of social service and community engagement among our cadets."
  },
  {
    icon: Compass,
    title: "Innovation in Education",
    description: "To continuously innovate our training methods and incorporate modern educational practices."
  }
]

const objectives = [
  "To develop character, comradeship, discipline, leadership, secular outlook, spirit of adventure and ideals of selfless service among the youth",
  "To create a human resource of organized, trained and motivated youth to provide leadership in all walks of life",
  "To provide a suitable environment to motivate the youth to take up a career in the armed forces",
  "To develop qualities of character, courage, comradeship, discipline, leadership, secular outlook, spirit of adventure and sportsmanship",
  "To create awareness about the rich cultural heritage of our country and develop respect for all religions and communities",
  "To develop capacity for hard work, honesty, devotion to duty and respect for others",
  "To create environmental awareness and promote activities for environmental protection",
  "To remove social evils and work towards creating a better society"
]

const coreValues = [
  {
    title: "Integrity",
    description: "Upholding honesty, transparency, and moral principles in all actions and decisions."
  },
  {
    title: "Patriotism",
    description: "Fostering love for the country and commitment to national service and security."
  },
  {
    title: "Excellence",
    description: "Striving for the highest standards in training, performance, and personal development."
  },
  {
    title: "Inclusivity",
    description: "Embracing diversity and promoting unity among cadets from all backgrounds."
  },
  {
    title: "Innovation",
    description: "Encouraging creative thinking and modern approaches to traditional challenges."
  },
  {
    title: "Service",
    description: "Dedicating ourselves to serving the community and the nation with selflessness."
  }
]

export default function MissionVisionPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Mission & Vision
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Our guiding principles and aspirations for developing future leaders and serving the nation
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 ncc-flag-gradient"></div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-blue-100 rounded-full">
                <Target className="h-12 w-12 text-blue-600" />
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Mission
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              The fundamental purpose that drives our NCC unit and guides all our activities and programs
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <div className="bg-blue-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-blue-900 mb-6">
                  NCC Mission Statement
                </h3>
                <p className="text-lg text-blue-800 leading-relaxed mb-6">
                  "To develop character, comradeship, discipline, leadership, secular outlook, 
                  spirit of adventure and ideals of selfless service among the youth of the country."
                </p>
                <p className="text-blue-700">
                  This mission forms the cornerstone of all our training programs and activities, 
                  ensuring that every cadet develops into a responsible citizen and future leader.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="relative h-80 rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="/about/mission.jpg"
                  alt="NCC Mission"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {missionPoints.map((point, index) => {
              const Icon = point.icon
              return (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      {point.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {point.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Vision Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-green-100 rounded-full">
                <Eye className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Vision
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our aspirational goals and the future we envision for our NCC unit and cadets
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div className="relative">
              <div className="relative h-80 rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="/about/vision.jpg"
                  alt="NCC Vision"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
            <div>
              <div className="bg-green-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-green-900 mb-6">
                  Our Vision Statement
                </h3>
                <p className="text-lg text-green-800 leading-relaxed mb-6">
                  "To be the leading NCC unit in Tamil Nadu, recognized for excellence in character 
                  building, leadership development, and producing patriotic citizens who serve the 
                  nation with distinction and integrity."
                </p>
                <p className="text-green-700">
                  We envision a future where our cadets become exemplary leaders in all spheres 
                  of life, contributing meaningfully to national development and security.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {visionElements.map((element, index) => {
              const Icon = element.icon
              return (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 text-center">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <Icon className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    {element.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {element.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Objectives Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Objectives
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Specific goals that guide our training programs and activities
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {objectives.map((objective, index) => (
              <div key={index} className="flex items-start space-x-4 p-6 bg-gray-50 rounded-xl">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">{index + 1}</span>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed">
                  {objective}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              The fundamental beliefs and principles that shape our culture and guide our actions
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreValues.map((value, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Be Part of Our Mission
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Join us in our mission to develop character, leadership, and service. 
            Help us build a stronger, more unified nation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/enrollment"
              className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Join Our Mission
            </Link>
            <Link
              href="/about"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold rounded-lg transition-colors duration-200"
            >
              Learn More About NCC
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
